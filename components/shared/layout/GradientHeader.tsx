import React from 'react';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { Box } from '@/components/ui/box';
import { Heading } from '@/components/ui/heading';
import { Text } from '@/components/ui/text';

interface GradientHeaderProps {
  title: string;
  subtitle?: string;
  colors?: string[];
  icon?: React.ReactNode;
  children?: React.ReactNode;
}

export function GradientHeader({
  title,
  subtitle,
  colors = ['#667eea', '#764ba2'],
  icon,
  children,
}: GradientHeaderProps) {
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={colors}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{ paddingTop: insets.top + 32, paddingBottom: 40 }}
    >
      <VStack className="items-center px-6">
        {icon && (
          <Box className="w-16 h-16 rounded-full bg-white/20 items-center justify-center mb-4">
            {icon}
          </Box>
        )}
        <Heading size="xl" className="text-white font-bold mb-2 text-center">
          {title}
        </Heading>
        {subtitle && (
          <Text size="sm" className="text-white/80 text-center">
            {subtitle}
          </Text>
        )}
        {children}
      </VStack>
    </LinearGradient>
  );
}
