import React from 'react';
import { Card } from '@/components/ui/card';
import { VStack } from '@/components/ui/vstack';
import { Heading } from '@/components/ui/heading';
import { Text } from '@/components/ui/text';

interface AppCardProps {
  title?: string;
  subtitle?: string;
  children: React.ReactNode;
  className?: string;
}

export function AppCard({ title, subtitle, children, className }: AppCardProps) {
  return (
    <Card className={`p-6 rounded-xl ${className || ''}`} variant="elevated">
      {(title || subtitle) && (
        <VStack className="mb-4">
          {title && (
            <Heading size="md" className="mb-1 text-typography-900">
              {title}
            </Heading>
          )}
          {subtitle && (
            <Text size="sm" className="text-typography-600">
              {subtitle}
            </Text>
          )}
        </VStack>
      )}
      {children}
    </Card>
  );
}
