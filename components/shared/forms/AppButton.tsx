import React from 'react';
import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { HStack } from '@/components/ui/hstack';

interface AppButtonProps {
  title: string;
  onPress?: () => void;
  variant?: 'solid' | 'outline' | 'link';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  isDisabled?: boolean;
  className?: string;
}

export function AppButton({ 
  title, 
  onPress, 
  variant = 'solid',
  size = 'md',
  leftIcon,
  rightIcon,
  isDisabled = false,
  className 
}: AppButtonProps) {
  return (
    <Button 
      onPress={onPress}
      variant={variant}
      size={size}
      isDisabled={isDisabled}
      className={className}
    >
      <HStack className="items-center">
        {leftIcon}
        <Text 
          size={size === 'xs' ? 'xs' : size === 'sm' ? 'sm' : 'md'} 
          className={`font-semibold ${leftIcon ? 'ml-2' : ''} ${rightIcon ? 'mr-2' : ''} ${
            variant === 'solid' ? 'text-white' : 'text-primary-600'
          }`}
        >
          {title}
        </Text>
        {rightIcon}
      </HStack>
    </Button>
  );
}
