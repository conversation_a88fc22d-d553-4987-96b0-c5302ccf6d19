import React from 'react';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Heading } from '@/components/ui/heading';

interface EarnStepProps {
  number: number;
  title: string;
  description: string;
  color: string;
}

function EarnStep({ number, title, description, color }: EarnStepProps) {
  return (
    <HStack className="items-start mb-6">
      <Box 
        className="w-8 h-8 rounded-lg items-center justify-center mr-4 mt-1"
        style={{ backgroundColor: color }}
      >
        <Text size="sm" className="text-white font-bold">
          {number}
        </Text>
      </Box>
      
      <VStack className="flex-1">
        <Text size="md" className="text-gray-900 font-semibold mb-1">
          {title}
        </Text>
        <Text size="sm" className="text-gray-600 leading-5">
          {description}
        </Text>
      </VStack>
    </HStack>
  );
}

export function HowToEarnSection() {
  const steps = [
    {
      number: 1,
      title: "Visit a participating business",
      description: "Look for the Indie Points logo at local businesses",
      color: "#3b82f6"
    },
    {
      number: 2,
      title: "Show your QR code",
      description: "Let the business scan your unique QR code before or after purchase",
      color: "#f59e0b"
    },
    {
      number: 3,
      title: "Earn points automatically",
      description: "Get 1 point for every £1 spent at participating businesses",
      color: "#ef4444"
    }
  ];

  return (
    <Box className="px-4 mt-8">
      <Heading size="lg" className="text-gray-900 font-bold mb-6">
        How to Earn Points
      </Heading>
      
      <VStack>
        {steps.map((step) => (
          <EarnStep
            key={step.number}
            number={step.number}
            title={step.title}
            description={step.description}
            color={step.color}
          />
        ))}
      </VStack>
    </Box>
  );
}
