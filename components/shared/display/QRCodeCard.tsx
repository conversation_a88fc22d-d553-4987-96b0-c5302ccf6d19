import React from 'react';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import QRCode from 'react-native-qrcode-svg';

interface QRCodeCardProps {
  value: string;
  size?: number;
  title?: string;
}

export function QRCodeCard({ value, size = 200, title }: QRCodeCardProps) {
  return (
    <VStack className="items-center">
      {title && (
        <Text size="lg" className="text-gray-900 font-semibold mb-4 text-center">
          {title}
        </Text>
      )}
      
      <Box 
        className="bg-white rounded-3xl p-6 border-4 border-gray-900"
        style={{
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 8,
        }}
      >
        <QRCode
          value={value}
          size={size}
          color="#000000"
          backgroundColor="#ffffff"
        />
      </Box>
    </VStack>
  );
}
