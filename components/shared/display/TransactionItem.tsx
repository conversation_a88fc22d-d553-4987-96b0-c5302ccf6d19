import React from 'react';
import { Box } from '@/components/ui/box';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { ShoppingBag, Gift } from 'lucide-react-native';

interface TransactionItemProps {
  businessName: string;
  type: 'purchase' | 'redemption';
  category: string;
  date: string;
  amount?: string;
  points: number;
}

export function TransactionItem({ 
  businessName, 
  type, 
  category, 
  date, 
  amount, 
  points 
}: TransactionItemProps) {
  const isPurchase = type === 'purchase';
  const iconColor = isPurchase ? '#3b82f6' : '#ef4444';
  const pointsColor = isPurchase ? 'text-green-600' : 'text-red-600';
  const pointsPrefix = isPurchase ? '+' : '-';
  
  return (
    <Box className="bg-white rounded-2xl p-4 mb-3 border border-gray-100">
      <HStack className="items-center justify-between">
        <HStack className="items-center flex-1">
          <Box className={`w-12 h-12 rounded-xl items-center justify-center ${isPurchase ? 'bg-blue-50' : 'bg-red-50'}`}>
            {isPurchase ? (
              <ShoppingBag size={20} color={iconColor} strokeWidth={2} />
            ) : (
              <Gift size={20} color={iconColor} strokeWidth={2} />
            )}
          </Box>
          
          <VStack className="ml-3 flex-1">
            <Text size="md" className="text-gray-900 font-semibold">
              {businessName}
            </Text>
            <Text size="sm" className="text-gray-500">
              {type === 'purchase' ? 'Purchase' : 'Redemption'} • {category}
            </Text>
            <Text size="xs" className="text-gray-400">
              {date}
            </Text>
            {amount && (
              <Text size="xs" className="text-gray-600 font-medium">
                {amount} spent
              </Text>
            )}
          </VStack>
        </HStack>
        
        <VStack className="items-end">
          <Text size="md" className={`font-bold ${pointsColor}`}>
            {pointsPrefix}{Math.abs(points)} pts
          </Text>
        </VStack>
      </HStack>
    </Box>
  );
}
