import React from 'react';
import { LinearGradient } from 'expo-linear-gradient';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';

interface PointsCardProps {
  title: string;
  points: number;
  colors: string[];
  size?: 'small' | 'large';
}

export function PointsCard({ title, points, colors, size = 'small' }: PointsCardProps) {
  const isLarge = size === 'large';
  
  return (
    <LinearGradient
      colors={colors}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{
        borderRadius: 16,
        padding: isLarge ? 24 : 16,
        flex: isLarge ? 1 : undefined,
        minHeight: isLarge ? 120 : 80,
      }}
    >
      <VStack className="justify-center h-full">
        <Text 
          size={isLarge ? "sm" : "xs"} 
          className="text-white/80 font-medium mb-1"
        >
          {title}
        </Text>
        <Text 
          size={isLarge ? "3xl" : "xl"} 
          className="text-white font-bold"
        >
          {points.toLocaleString()}
        </Text>
      </VStack>
    </LinearGradient>
  );
}
