import React from 'react';
import { ScrollView } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Heading } from '@/components/ui/heading';
import { Button } from '@/components/ui/button';

export default function WelcomeScreen() {
  const insets = useSafeAreaInsets();

  const features = [
    {
      title: "Earn points",
      description: "Get points for every purchase at participating businesses",
      color: "#3b82f6"
    },
    {
      title: "Redeem rewards",
      description: "Use your points to get discounts and free items",
      color: "#f59e0b"
    },
    {
      title: "Support local",
      description: "Help your favorite local businesses grow and thrive",
      color: "#ef4444"
    }
  ];

  return (
    <ScrollView
      className="flex-1 bg-white"
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{
        paddingBottom: insets.bottom + 40,
      }}
    >
      {/* Header with three-color gradient */}
      <LinearGradient
        colors={['#3b82f6', '#f59e0b', '#ef4444']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={{ 
          paddingTop: insets.top + 60, 
          paddingBottom: 60,
          paddingHorizontal: 16 
        }}
      >
        <VStack className="items-center">
          <Heading size="3xl" className="text-white font-bold mb-4 text-center">
            Welcome to Indie Points
          </Heading>
          <Text size="md" className="text-white/90 text-center">
            Earn points, get rewards, support local businesses
          </Text>
        </VStack>
      </LinearGradient>

      <Box className="px-6 pt-12">
        {/* Features */}
        <VStack className="space-y-8 mb-12">
          {features.map((feature, index) => (
            <HStack key={index} className="items-start">
              <Box 
                className="w-12 h-12 rounded-2xl items-center justify-center mr-4 mt-1"
                style={{ backgroundColor: feature.color }}
              >
                <Box 
                  className="w-6 h-6 rounded-lg"
                  style={{ backgroundColor: 'rgba(255,255,255,0.3)' }}
                />
              </Box>
              
              <VStack className="flex-1">
                <Text size="xl" className="text-gray-900 font-semibold mb-2">
                  {feature.title}
                </Text>
                <Text size="md" className="text-gray-600 leading-6">
                  {feature.description}
                </Text>
              </VStack>
            </HStack>
          ))}
        </VStack>

        {/* Action Buttons */}
        <VStack className="space-y-4">
          <Button
            onPress={() => router.push('/sign-up')}
            className="bg-blue-600 rounded-2xl py-4 px-6"
          >
            <Text className="text-white font-semibold text-center text-lg">
              Get started
            </Text>
          </Button>

          <HStack className="items-center justify-center">
            <Text size="md" className="text-gray-600">
              Already have an account?{' '}
            </Text>
            <Button
              variant="link"
              onPress={() => router.push('/sign-in')}
              className="p-0"
            >
              <Text size="md" className="text-blue-600 font-semibold">
                Sign in
              </Text>
            </Button>
          </HStack>
        </VStack>
      </Box>
    </ScrollView>
  );
}
