import { Sc<PERSON>View } from 'react-native';
import { User, Mail, MapPin, Calendar } from 'lucide-react-native';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import {
  GradientHeader,
  AppCard,
  InfoItem,
  StatCard,
} from '@/components/shared';

export default function ProfileScreen() {
  return (
    <ScrollView
      className="flex-1 bg-background-50"
      showsVerticalScrollIndicator={false}
    >
      <GradientHeader
        title="John Doe"
        subtitle="Mobile Developer"
        colors={['#4facfe', '#00f2fe']}
        icon={<User size={40} color="#ffffff" strokeWidth={2} />}
      />

      <Box className="flex-1 px-6 pt-6">
        <AppCard title="Contact Information" className="mb-6">
          <VStack className="space-y-1">
            <InfoItem
              icon={<Mail size={20} color="#6366f1" strokeWidth={2} />}
              label="Email"
              value="<EMAIL>"
              className="border-b border-outline-100 last:border-b-0"
            />
            <InfoItem
              icon={<MapPin size={20} color="#6366f1" strokeWidth={2} />}
              label="Location"
              value="San Francisco, CA"
              className="border-b border-outline-100 last:border-b-0"
            />
            <InfoItem
              icon={<Calendar size={20} color="#6366f1" strokeWidth={2} />}
              label="Joined"
              value="January 2024"
              className="border-b border-outline-100 last:border-b-0"
            />
          </VStack>
        </AppCard>

        <AppCard title="Statistics" className="mb-10">
          <HStack className="justify-around">
            <StatCard value="42" label="Projects" />
            <StatCard value="1.2k" label="Commits" />
            <StatCard value="128" label="Stars" />
          </HStack>
        </AppCard>
      </Box>
    </ScrollView>
  );
}
