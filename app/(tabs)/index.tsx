import { Sc<PERSON>View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Sparkles, Heart, Code } from 'lucide-react-native';
import { useState } from 'react';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/button';
import { GradientHeader, AppCard } from '@/components/shared';

export default function HomeScreen() {
  const [pressed, setPressed] = useState(false);
  const insets = useSafeAreaInsets();

  return (
    <ScrollView
      className="flex-1 bg-background-50"
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{
        paddingBottom: insets.bottom + 100, // Extra space for tab bar
      }}
    >
      <GradientHeader
        title="Hello World"
        subtitle="Beautiful React Native with Expo"
        colors={['#667eea', '#764ba2']}
        icon={<Sparkles size={32} color="#ffffff" strokeWidth={2} />}
      >
        <Text size="sm" className="text-white/80 mt-2">
          Welcome to
        </Text>
      </GradientHeader>

      <Box className="flex-1 px-4 pt-6">
        <AppCard title="Built with Modern Tech" className="mb-6">
          <HStack className="items-center mb-3">
            <Code size={24} color="#6366f1" strokeWidth={2} />
            <Text size="md" className="ml-2 text-typography-900 font-semibold">
              Latest Technologies
            </Text>
          </HStack>
          <Text size="sm" className="text-typography-600 leading-5">
            This app showcases React Native, Expo Router, TypeScript, and
            Gluestack UI working together to create a seamless mobile
            experience.
          </Text>
        </AppCard>

        <Button
          onPress={() => setPressed(!pressed)}
          className="bg-primary-600 rounded-xl py-4 px-6 mb-6 shadow-lg"
        >
          <HStack className="items-center justify-center">
            <Heart
              size={20}
              color="#ffffff"
              fill={pressed ? '#ffffff' : 'transparent'}
              strokeWidth={2}
            />
            <Text className="text-white font-semibold ml-2">
              {pressed ? 'Thanks!' : 'Tap to Like'}
            </Text>
          </HStack>
        </Button>

        <AppCard title="Features" className="mb-8">
          <VStack className="space-y-3">
            {[
              'Tab-based Navigation',
              'Beautiful Gradients',
              'Interactive Elements',
              'Responsive Design',
              'Gluestack UI Components',
            ].map((feature, index) => (
              <HStack key={index} className="items-center">
                <Box className="w-1.5 h-1.5 rounded-full bg-primary-600 mr-3" />
                <Text size="sm" className="text-typography-700 font-medium">
                  {feature}
                </Text>
              </HStack>
            ))}
          </VStack>
        </AppCard>
      </Box>
    </ScrollView>
  );
}
