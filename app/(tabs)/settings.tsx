import React from 'react';
import { ScrollView, Alert } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Building2 } from 'lucide-react-native';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Heading } from '@/components/ui/heading';
import { Button } from '@/components/ui/button';

export default function SettingsScreen() {
  const insets = useSafeAreaInsets();

  // Mock user data
  const userData = {
    email: '<EMAIL>',
    memberSince: '11 June 2025',
  };

  const handleSignOut = () => {
    Alert.alert('Sign Out', 'Are you sure you want to sign out?', [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Sign Out',
        style: 'destructive',
        onPress: () => {
          Alert.alert('Signed Out', 'You have been signed out successfully.');
        },
      },
    ]);
  };

  return (
    <ScrollView
      className="flex-1 bg-gray-50"
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{
        paddingBottom: insets.bottom + 100,
      }}
    >
      {/* Header with three-color gradient */}
      <LinearGradient
        colors={['#3b82f6', '#f59e0b', '#ef4444']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={{
          paddingTop: insets.top + 32,
          paddingBottom: 40,
          paddingHorizontal: 16,
        }}
      >
        <Heading size="2xl" className="text-white font-bold">
          Settings
        </Heading>
      </LinearGradient>

      <Box className="px-4 pt-8">
        {/* User Profile Card */}
        <Box
          className="bg-white rounded-2xl p-6 mb-6 border border-gray-100"
          style={{
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 4,
          }}
        >
          <HStack className="items-center">
            <Box className="w-16 h-16 rounded-2xl bg-blue-100 items-center justify-center mr-4">
              <Building2 size={28} color="#3b82f6" strokeWidth={2} />
            </Box>

            <VStack className="flex-1">
              <Text size="lg" className="text-gray-900 font-semibold mb-1">
                {userData.email}
              </Text>
              <Text size="sm" className="text-gray-500">
                Member since {userData.memberSince}
              </Text>
            </VStack>
          </HStack>
        </Box>

        {/* Sign Out Button */}
        <Button
          onPress={handleSignOut}
          className="bg-red-600 rounded-2xl py-4 px-6"
        >
          <Text className="text-white font-semibold text-center text-base">
            Sign Out
          </Text>
        </Button>
      </Box>
    </ScrollView>
  );
}
