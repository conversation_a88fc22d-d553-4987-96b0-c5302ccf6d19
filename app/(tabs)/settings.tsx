import { ScrollView, Pressable } from 'react-native';
import {
  Settings as SettingsIcon,
  Bell,
  Shield,
  Palette,
  Info,
  ChevronRight,
} from 'lucide-react-native';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { GradientHeader, AppCard } from '@/components/shared';

export default function SettingsScreen() {
  const settingsOptions = [
    {
      icon: Bell,
      title: 'Notifications',
      description: 'Manage your notification preferences',
      color: '#f59e0b',
    },
    {
      icon: Shield,
      title: 'Privacy & Security',
      description: 'Control your privacy settings',
      color: '#10b981',
    },
    {
      icon: Palette,
      title: 'Appearance',
      description: 'Customize the app appearance',
      color: '#8b5cf6',
    },
    {
      icon: Info,
      title: 'About',
      description: 'App version and information',
      color: '#06b6d4',
    },
  ];

  return (
    <ScrollView
      className="flex-1 bg-background-50"
      showsVerticalScrollIndicator={false}
    >
      <GradientHeader
        title="Settings"
        subtitle="Customize your experience"
        colors={['#a78bfa', '#ec4899']}
        icon={<SettingsIcon size={32} color="#ffffff" strokeWidth={2} />}
      />

      <Box className="flex-1 px-6 pt-6">
        <AppCard title="Preferences" className="mb-6">
          <VStack className="space-y-1">
            {settingsOptions.map((option, index) => (
              <Pressable
                key={index}
                className="flex-row items-center py-4 px-2 rounded-lg active:bg-background-100"
                onPress={() => {
                  // Handle setting option press
                }}
              >
                <Box
                  className="w-10 h-10 rounded-full items-center justify-center mr-3"
                  style={{ backgroundColor: option.color }}
                >
                  <option.icon size={20} color="#ffffff" strokeWidth={2} />
                </Box>
                <VStack className="flex-1">
                  <Text
                    size="md"
                    className="text-typography-900 font-semibold mb-1"
                  >
                    {option.title}
                  </Text>
                  <Text size="xs" className="text-typography-600">
                    {option.description}
                  </Text>
                </VStack>
                <ChevronRight size={20} color="#9ca3af" strokeWidth={2} />
              </Pressable>
            ))}
          </VStack>
        </AppCard>

        <AppCard title="App Information" className="mb-10">
          <VStack className="space-y-2">
            {[
              { label: 'Version', value: '1.0.0' },
              { label: 'Build', value: '2024.1.0' },
              { label: 'Platform', value: 'React Native' },
              { label: 'UI Library', value: 'Gluestack UI v2' },
            ].map((info, index) => (
              <HStack key={index} className="justify-between items-center py-2">
                <Text size="sm" className="text-typography-600">
                  {info.label}
                </Text>
                <Text size="sm" className="text-typography-900 font-medium">
                  {info.value}
                </Text>
              </HStack>
            ))}
          </VStack>
        </AppCard>
      </Box>
    </ScrollView>
  );
}
