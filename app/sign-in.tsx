import React, { useState } from 'react';
import { Sc<PERSON>View, Alert } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Heading } from '@/components/ui/heading';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { InputField } from '@/components/ui/input';

export default function SignInScreen() {
  const insets = useSafeAreaInsets();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleSignIn = () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }
    
    // Mock sign in - in a real app this would authenticate with a backend
    Alert.alert('Success', 'Signed in successfully!', [
      {
        text: 'OK',
        onPress: () => router.replace('/(tabs)'),
      },
    ]);
  };

  const handleSocialSignIn = (provider: string) => {
    Alert.alert('Social Sign In', `${provider} sign in would be implemented here`);
  };

  const handleForgotPassword = () => {
    Alert.alert('Forgot Password', 'Password reset functionality would be implemented here');
  };

  return (
    <ScrollView
      className="flex-1 bg-white"
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{
        paddingBottom: insets.bottom + 40,
      }}
    >
      {/* Header with three-color gradient */}
      <LinearGradient
        colors={['#3b82f6', '#f59e0b', '#ef4444']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={{ 
          paddingTop: insets.top + 40, 
          paddingBottom: 40,
          paddingHorizontal: 16 
        }}
      >
        <Heading size="2xl" className="text-white font-bold">
          Welcome Back
        </Heading>
      </LinearGradient>

      <Box className="px-6 pt-8">
        {/* Email Input */}
        <VStack className="mb-4">
          <Text size="md" className="text-gray-900 font-medium mb-2">
            Email <Text className="text-red-500">*</Text>
          </Text>
          <Input className="border border-gray-300 rounded-2xl">
            <InputField
              placeholder="Enter your email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              className="px-4 py-3"
            />
          </Input>
        </VStack>

        {/* Password Input */}
        <VStack className="mb-6">
          <Text size="md" className="text-gray-900 font-medium mb-2">
            Password <Text className="text-red-500">*</Text>
          </Text>
          <Input className="border border-gray-300 rounded-2xl">
            <InputField
              placeholder="Enter your password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              className="px-4 py-3"
            />
          </Input>
        </VStack>

        {/* Sign In Button */}
        <Button
          onPress={handleSignIn}
          className="bg-blue-600 rounded-2xl py-4 px-6 mb-8"
        >
          <Text className="text-white font-semibold text-center text-lg">
            Sign In
          </Text>
        </Button>

        {/* Divider */}
        <HStack className="items-center mb-8">
          <Box className="flex-1 h-px bg-gray-300" />
          <Text size="sm" className="text-gray-500 mx-4">
            or continue with
          </Text>
          <Box className="flex-1 h-px bg-gray-300" />
        </HStack>

        {/* Social Sign In Buttons */}
        <VStack className="space-y-3 mb-8">
          <Button
            onPress={() => handleSocialSignIn('Google')}
            variant="outline"
            className="border-gray-300 rounded-2xl py-4 px-6"
          >
            <HStack className="items-center justify-center">
              <Text className="text-gray-700 font-semibold text-base mr-2">G</Text>
              <Text className="text-gray-700 font-semibold text-base">
                Continue with Google
              </Text>
            </HStack>
          </Button>

          <Button
            onPress={() => handleSocialSignIn('Facebook')}
            variant="outline"
            className="border-gray-300 rounded-2xl py-4 px-6"
          >
            <HStack className="items-center justify-center">
              <Text className="text-blue-600 font-semibold text-base mr-2">f</Text>
              <Text className="text-gray-700 font-semibold text-base">
                Continue with Facebook
              </Text>
            </HStack>
          </Button>

          <Button
            onPress={() => handleSocialSignIn('Twitter')}
            variant="outline"
            className="border-gray-300 rounded-2xl py-4 px-6"
          >
            <HStack className="items-center justify-center">
              <Text className="text-gray-900 font-semibold text-base mr-2">𝕏</Text>
              <Text className="text-gray-700 font-semibold text-base">
                Continue with Twitter
              </Text>
            </HStack>
          </Button>
        </VStack>

        {/* Forgot Password */}
        <Button
          variant="link"
          onPress={handleForgotPassword}
          className="self-center"
        >
          <Text size="md" className="text-blue-600 font-semibold">
            Forgot Password?
          </Text>
        </Button>
      </Box>
    </ScrollView>
  );
}
