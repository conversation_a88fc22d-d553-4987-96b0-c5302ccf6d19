import { Link, Stack } from 'expo-router';
import { VStack } from '@/components/ui/vstack';
import { Heading } from '@/components/ui/heading';
import { Text } from '@/components/ui/text';
import { But<PERSON> } from '@/components/ui/button';

export default function NotFoundScreen() {
  return (
    <>
      <Stack.Screen options={{ title: 'Oops!' }} />
      <VStack className="flex-1 items-center justify-center p-5 bg-background-50">
        <Heading
          size="xl"
          className="text-typography-900 font-bold mb-4 text-center"
        >
          404
        </Heading>
        <Text size="lg" className="text-typography-700 mb-6 text-center">
          This screen does not exist.
        </Text>
        <Link href="/" asChild>
          <Button className="bg-primary-600 px-6 py-3">
            <Text className="text-white font-semibold">Go to home screen!</Text>
          </Button>
        </Link>
      </VStack>
    </>
  );
}
