{"name": "indie-points", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint --fix"}, "dependencies": {"@expo/html-elements": "0.4.2", "@expo/vector-icons": "^14.1.0", "@gluestack-ui/button": "1.0.14", "@gluestack-ui/nativewind-utils": "1.0.26", "@gluestack-ui/overlay": "0.1.22", "@gluestack-ui/themed": "1.1.73", "@gluestack-ui/toast": "1.0.9", "@lucide/lab": "^0.1.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "expo": "53.0.11", "expo-blur": "~14.1.5", "expo-camera": "~16.1.8", "expo-constants": "~17.1.3", "expo-dev-client": "~5.2.0", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-router": "~5.1.0", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.8", "expo-web-browser": "~14.1.6", "lucide-react-native": "^0.475.0", "nativewind": "4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "tailwindcss": "4.1.10"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3", "eslint": "^9.0.0", "eslint-config-expo": "~9.2.0"}}