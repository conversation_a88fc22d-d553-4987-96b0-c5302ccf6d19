{"expo": {"name": "bolt-expo-nativewind", "slug": "bolt-expo-nativewind", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": false, "ios": {"supportsTablet": true, "bundleIdentifier": "com.indiestuart.boltexponativewind", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", "expo-web-browser"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "17539d1f-9376-4e25-a6e7-a5c960b966a6"}}, "owner": "indiestuart"}}