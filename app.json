{"expo": {"name": "Indie Points", "slug": "indie-points", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "indiepoints", "userInterfaceStyle": "light", "newArchEnabled": false, "ios": {"supportsTablet": true, "bundleIdentifier": "com.indiestuart.indiepoints", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSCameraUsageDescription": "This app uses the camera to scan QR codes for loyalty points."}}, "android": {"permissions": ["CAMERA"]}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", "expo-web-browser", "expo-camera", "expo-barcode-scanner"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "17539d1f-9376-4e25-a6e7-a5c960b966a6"}}, "owner": "indiestuart"}}